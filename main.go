package main

import (
	"fmt"
	"go_solve_api/config"
	"go_solve_api/handlers"
	"go_solve_api/utils"
	"log"
	"net/http"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()
	
	// 初始化日志器
	logger := utils.NewLogger()
	
	// 验证必要的配置
	if cfg.QwenAPIKey == "" {
		log.Fatal("QWEN_API_KEY environment variable is required")
	}
	if cfg.DeepseekAPIKey == "" {
		log.Fatal("DEEPSEEK_API_KEY environment variable is required")
	}
	
	// 初始化处理器
	examHandler := handlers.NewExamHandler(cfg, logger)
	
	// 设置路由
	http.HandleFunc("/api/v1/exam/process", examHandler.ProcessExam)
	http.HandleFunc("/api/v1/health", examHandler.HealthCheck)
	
	// 添加根路径处理
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{
			"service": "go_solve_api",
			"version": "1.0.0",
			"endpoints": {
				"process_exam": "/api/v1/exam/process",
				"health_check": "/api/v1/health"
			},
			"description": "API service for processing exam questions using Qwen and Deepseek"
		}`)
	})
	
	logger.Info("Starting server", map[string]interface{}{
		"port":         cfg.ServerPort,
		"qwen_model":   cfg.QwenModel,
		"deepseek_model": cfg.DeepseekModel,
	})
	
	log.Printf("Server starting on port %s", cfg.ServerPort)
	log.Printf("Endpoints:")
	log.Printf("  POST /api/v1/exam/process - Process exam questions")
	log.Printf("  GET  /api/v1/health - Health check")
	log.Printf("  GET  / - Service information")
	
	if err := http.ListenAndServe(":"+cfg.ServerPort, nil); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
