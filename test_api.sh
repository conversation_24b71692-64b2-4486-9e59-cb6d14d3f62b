#!/bin/bash

# Go Solve API 测试脚本

echo "=== Go Solve API 测试 ==="
echo

# 设置服务器地址
SERVER_URL="http://localhost:8080"

echo "1. 测试健康检查接口..."
curl -X GET "${SERVER_URL}/api/v1/health" \
  -H "Content-Type: application/json" \
  -w "\n\n"

echo "2. 测试服务信息接口..."
curl -X GET "${SERVER_URL}/" \
  -H "Content-Type: application/json" \
  -w "\n\n"

echo "3. 测试考试题目处理接口..."
echo "注意：请确保已设置正确的 API Keys 环境变量"
echo

# 示例图片 URL（请替换为实际的考试题目图片 URL）
IMAGE_URL="https://example.com/exam-image.jpg"

curl -X POST "${SERVER_URL}/api/v1/exam/process" \
  -H "Content-Type: application/json" \
  -d "{
    \"image_url\": \"${IMAGE_URL}\"
  }" \
  -w "\n\n"

echo "=== 测试完成 ==="
echo
echo "使用方法："
echo "1. 设置环境变量："
echo "   export QWEN_API_KEY='your_qwen_api_key'"
echo "   export DEEPSEEK_API_KEY='your_deepseek_api_key'"
echo
echo "2. 启动服务："
echo "   go run main.go"
echo
echo "3. 在另一个终端运行测试："
echo "   chmod +x test_api.sh"
echo "   ./test_api.sh"
echo
echo "或者直接使用 cURL 命令："
echo
echo "# 健康检查"
echo "curl -X GET http://localhost:8080/api/v1/health"
echo
echo "# 处理考试题目（请替换为实际的图片 URL）"
echo "curl -X POST http://localhost:8080/api/v1/exam/process \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"image_url\": \"https://your-image-url.jpg\""
echo "  }'"
