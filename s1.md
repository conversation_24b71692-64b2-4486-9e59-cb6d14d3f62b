帮我使用go语言开发一个web api服务，你需要先分析下述需求，然后在开始处理。

生产环境计划 SDK版本为 1.24.0 ，框架约定 gin；约定数据库Mysql8，需要redis。

遵循严格且严谨的原则开发。尽可能的将方法封装函数，最大化的面向对象开发。使用良好的命名方法。使用专业的结构架构，做好功能分化。

项目简介【一款提供图片搜题的api服务，三方提交一张图片的url地址，api将url提交给qwen大模型，然后返回解析数据给api，api拿到解析数据后，再将数据提交给deepseek大模型，最后deepseek大模型会返回最重的结果。api再将结果返回给三方。 这里开发的只是一个web的api服务，不需要考虑前端，包括管理后台，只处理接口就可以，】


与大模型的交互直接 HTTP REST API 调用qwen跟deepseek。不要集成sdk

业务流程是


1. 将图片的url提交给qwen-vl-plus大模型。
2. 将qwen返回的原始数据发送给deepseek-chat大模型。
3. 得到deepseek-chat返回的最终结果。
4. 需要输出日志（日志需要存储到数据库中，需要对日志进行拆分，让他更方便查看。）
    4.1. 需要记录qwen-vl-plus的返回结果,需要记录qwen-vl-plus的响应时间以及token的消耗情况
    4.2. 需要记录deepseek-chat的返回结果，需要记录qdeepseek的响应时间以及token的消耗情况

5. 对qwen的约束声明请使用下述方案，这是一个python的方案，请除了进行语法调整外，不要进行内容修改。不然会导致qwen返回数据出现不可预料的错误。


**
        payload = {
            "model": self.model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "image": image_url
                            },
                            {
                                "text": """请精准识别图片中的考试题目内容。图片是考试系统的截图，包含考试题目和选项。

请按照以下格式输出：
1. 题目类型：（单选题/多选题/判断题）
2. 题目内容：（完整的题干）
3. 选项内容：（所有选项的完整内容）
4. 题干图片：（如果题目下方有图片，请描述图片内容）

要求：
- 完整准确地识别所有文字内容
- 不要解答题目，只需要识别题目内容
- 保持原有的格式和选项标识（A、B、C、D或Y、N）"""
                            }
                        ]
                    }
                ]
            },
            "parameters": {
                "result_format": "message"
            }
        }**

6. 对deepseek的约束，请帮我参考qwen的约束声明，然后进行适当的修改。但是严格执行的内容必须保证。

        这是一道驾照考试的题目，请认真分析后给我做出解答，需要保证答案的置信度为100%。

        [严格执行]
        需要严格按照以下格式返回内容；
        题目类型：[单选题/多选题/判断题]
        题目内容：[问题的完整内容，不包含序号]
        选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]
        正确答案；[A,B,C,D]
        答案解析：[答案解析内容]

        【严格执行】
        判断题时需要把Y与N映射为A与B，所有的标点符号都需要用英文符号。不可以出现多余的空格。

7. 需要admin的管理后台接口。管理后台允许用户登录注册，用于生成key。
    7.1 用户注册/登录/忘记密码。
            注册；手机号+短信验证码+密码+确认密码+邀请码（邀请码只有一个，可在管理员后台配置）
            登录：手机号+密码；需要建立防爆破的机制。
            忘记密码：手机号+短信验证码+新密码+确认密码；仅注册过的用户可用。考虑短信验证码爆破风险。
    7.2 用户功能；
        7.2.1 应用管理-创建应用-删除应用-重置AccessKey Secret-查看应用列表
                每个用户最多创建5个应用。输入应用名称与备注后创建
                创建应用-成功会自动生成AccessKey ID 与AccessKey Secret。
                AccessKey Secret允许重置。
                AccessKey ID 的长度16位，大小写+数字
                AccessKey Secret 的长度32位，大小写+数字

        7.2.2 消费点-消费点余额
        7.2.3 每个应用的api请求记录。
        7.2.4 数据大屏，最近30天的每日api请求次数。成功与失败的次数。以及总的次数。
    7.3 管理员功能；
        用户管理--用户信息查看--冻结/解冻用户--增加/扣除用户消费点-查看用户应用列表。
        应用管理--应用信息查看--冻结/解冻应用--应用qps请求阈值设置。默认每秒5次请求。

    7.4 数据大屏，今日全网api请求次数。成功与失败的次数。以及总的次数。



        

        AccessKey ID 与AccessKey Secret的创建与管理，用于api请求时的验证。