package utils

import (
	"encoding/json"
	"log"
	"time"
)

type Logger struct{}

func NewLogger() *Logger {
	return &Logger{}
}

func (l *Logger) Info(message string, data interface{}) {
	logEntry := map[string]interface{}{
		"level":     "INFO",
		"timestamp": time.Now().Format(time.RFC3339),
		"message":   message,
		"data":      data,
	}
	l.logJSON(logEntry)
}

func (l *Logger) Error(message string, err error, data interface{}) {
	logEntry := map[string]interface{}{
		"level":     "ERROR",
		"timestamp": time.Now().Format(time.RFC3339),
		"message":   message,
		"error":     err.Error(),
		"data":      data,
	}
	l.logJSON(logEntry)
}

func (l *Logger) APICall(service string, startTime, endTime time.Time, tokenUsage interface{}, success bool, errorMsg string) {
	duration := endTime.Sub(startTime)
	logEntry := map[string]interface{}{
		"level":       "INFO",
		"timestamp":   time.Now().Format(time.RFC3339),
		"message":     "API Call",
		"service":     service,
		"start_time":  startTime.Format(time.RFC3339),
		"end_time":    endTime.Format(time.RFC3339),
		"duration":    duration.String(),
		"token_usage": tokenUsage,
		"success":     success,
	}
	if errorMsg != "" {
		logEntry["error"] = errorMsg
	}
	l.logJSON(logEntry)
}

func (l *Logger) logJSON(entry map[string]interface{}) {
	jsonData, err := json.Marshal(entry)
	if err != nil {
		log.Printf("Failed to marshal log entry: %v", err)
		return
	}
	log.Println(string(jsonData))
}
