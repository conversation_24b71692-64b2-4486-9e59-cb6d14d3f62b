package utils

import (
	"encoding/json"
	"io"
	"log"
	"os"
	"time"
)

type Logger struct{
	generalLogger *log.Logger
	qwenLogger    *log.Logger
}

func NewLogger() *Logger {
	// 创建 logs 目录
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Printf("Failed to create logs directory: %v", err)
	}

	// 创建通用日志文件
	generalFile, err := os.OpenFile("logs/app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("Failed to open general log file: %v", err)
		generalFile = os.Stdout
	}

	// 创建 Qwen 专门日志文件
	qwenFile, err := os.OpenFile("logs/qwen_text.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Printf("Failed to open qwen log file: %v", err)
		qwenFile = os.Stdout
	}

	// 创建多重输出（同时输出到控制台和文件）
	generalMultiWriter := io.MultiWriter(os.Stdout, generalFile)
	qwenMultiWriter := io.MultiWriter(os.Stdout, qwenFile)

	return &Logger{
		generalLogger: log.New(generalMultiWriter, "", log.LstdFlags),
		qwenLogger:    log.New(qwenMultiWriter, "", log.LstdFlags),
	}
}

func (l *Logger) Info(message string, data interface{}) {
	logEntry := map[string]interface{}{
		"level":     "INFO",
		"timestamp": time.Now().Format(time.RFC3339),
		"message":   message,
		"data":      data,
	}
	l.logJSON(logEntry)
}

func (l *Logger) Error(message string, err error, data interface{}) {
	logEntry := map[string]interface{}{
		"level":     "ERROR",
		"timestamp": time.Now().Format(time.RFC3339),
		"message":   message,
		"error":     err.Error(),
		"data":      data,
	}
	l.logJSON(logEntry)
}

func (l *Logger) APICall(service string, startTime, endTime time.Time, tokenUsage interface{}, success bool, errorMsg string) {
	duration := endTime.Sub(startTime)
	logEntry := map[string]interface{}{
		"level":       "INFO",
		"timestamp":   time.Now().Format(time.RFC3339),
		"message":     "API Call",
		"service":     service,
		"start_time":  startTime.Format(time.RFC3339),
		"end_time":    endTime.Format(time.RFC3339),
		"duration":    duration.String(),
		"token_usage": tokenUsage,
		"success":     success,
	}
	if errorMsg != "" {
		logEntry["error"] = errorMsg
	}
	l.logJSON(logEntry)
}

func (l *Logger) logJSON(entry map[string]interface{}) {
	jsonData, err := json.Marshal(entry)
	if err != nil {
		l.generalLogger.Printf("Failed to marshal log entry: %v", err)
		return
	}
	l.generalLogger.Println(string(jsonData))
}

// LogQwenText 专门记录 Qwen 文本日志到独立文件
func (l *Logger) LogQwenText(returnTime time.Time, textContent string) {
	// 格式化时间
	timeStr := returnTime.Format("2006-01-02 15:04:05.000")

	// 输出到专门的 Qwen 日志文件
	l.qwenLogger.Printf("QWEN_TEXT_LOG")
	l.qwenLogger.Printf("%s", timeStr)
	l.qwenLogger.Printf("%s", textContent)
	l.qwenLogger.Printf("--- END QWEN_TEXT_LOG ---")
}
