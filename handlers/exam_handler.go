package handlers

import (
	"encoding/json"
	"go_solve_api/config"
	"go_solve_api/models"
	"go_solve_api/services"
	"go_solve_api/utils"
	"net/http"
)

type ExamHandler struct {
	qwenService     *services.QwenService
	deepseekService *services.DeepseekService
	logger          *utils.Logger
}

func NewExamHandler(cfg *config.Config, logger *utils.Logger) *ExamHandler {
	return &ExamHandler{
		qwenService:     services.NewQwenService(cfg, logger),
		deepseekService: services.NewDeepseekService(cfg, logger),
		logger:          logger,
	}
}

func (h *ExamHandler) ProcessExam(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != http.MethodPost {
		h.sendErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req models.ExamRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.logger.Error("Failed to decode request", err, nil)
		h.sendErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if req.ImageURL == "" {
		h.sendErrorResponse(w, http.StatusBadRequest, "image_url is required")
		return
	}

	h.logger.Info("Processing exam request", map[string]interface{}{
		"image_url": req.ImageURL,
	})

	// 步骤1: 调用 Qwen 分析图片
	qwenResult, qwenLog, err := h.qwenService.AnalyzeImage(req.ImageURL)
	if err != nil {
		h.logger.Error("Qwen analysis failed", err, map[string]interface{}{
			"image_url": req.ImageURL,
		})
		response := &models.ExamResponse{
			Success: false,
			Message: "Failed to analyze image with Qwen: " + err.Error(),
			ProcessingLog: &models.ProcessingLog{
				QwenLog: qwenLog,
			},
		}
		h.sendResponse(w, http.StatusInternalServerError, response)
		return
	}

	// 步骤2: 将 Qwen 结果发送给 Deepseek
	deepseekResult, deepseekLog, err := h.deepseekService.AnswerQuestion(qwenResult.RawContent)
	if err != nil {
		h.logger.Error("Deepseek analysis failed", err, map[string]interface{}{
			"qwen_content": qwenResult.RawContent,
		})
		response := &models.ExamResponse{
			Success:    false,
			Message:    "Failed to get answer from Deepseek: " + err.Error(),
			QwenResult: qwenResult,
			ProcessingLog: &models.ProcessingLog{
				QwenLog:     qwenLog,
				DeepseekLog: deepseekLog,
			},
		}
		h.sendResponse(w, http.StatusInternalServerError, response)
		return
	}

	// 步骤3: 返回最终结果
	response := &models.ExamResponse{
		Success:        true,
		Message:        "Exam processed successfully",
		QwenResult:     qwenResult,
		DeepseekResult: deepseekResult,
		ProcessingLog: &models.ProcessingLog{
			QwenLog:     qwenLog,
			DeepseekLog: deepseekLog,
		},
	}

	h.logger.Info("Exam processing completed successfully", map[string]interface{}{
		"image_url":      req.ImageURL,
		"qwen_tokens":    qwenResult.Usage.TotalTokens,
		"deepseek_tokens": deepseekResult.Usage.TotalTokens,
		"answer":         deepseekResult.Answer,
	})

	h.sendResponse(w, http.StatusOK, response)
}

func (h *ExamHandler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	if r.Method != http.MethodGet {
		h.sendErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	response := map[string]interface{}{
		"status":  "healthy",
		"service": "go_solve_api",
		"version": "1.0.0",
	}

	h.sendResponse(w, http.StatusOK, response)
}

func (h *ExamHandler) sendResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.WriteHeader(statusCode)
	if err := json.NewEncoder(w).Encode(data); err != nil {
		h.logger.Error("Failed to encode response", err, data)
	}
}

func (h *ExamHandler) sendErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	response := map[string]interface{}{
		"success": false,
		"message": message,
	}
	h.sendResponse(w, statusCode, response)
}
