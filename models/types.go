package models

import "time"

// ExamRequest 考试题目识别请求
type ExamRequest struct {
	ImageURL string `json:"image_url" binding:"required"`
}

// ExamResponse 最终响应
type ExamResponse struct {
	Success      bool                 `json:"success"`
	Message      string               `json:"message,omitempty"`
	QwenResult   *QwenAnalysisResult  `json:"qwen_result,omitempty"`
	DeepseekResult *DeepseekAnswerResult `json:"deepseek_result,omitempty"`
	ProcessingLog *ProcessingLog       `json:"processing_log,omitempty"`
}

// QwenRequest qwen API 请求结构
type QwenRequest struct {
	Model      string                 `json:"model"`
	Input      QwenInput             `json:"input"`
	Parameters QwenParameters        `json:"parameters"`
}

type QwenInput struct {
	Messages []QwenMessage `json:"messages"`
}

type QwenMessage struct {
	Role    string        `json:"role"`
	Content []QwenContent `json:"content"`
}

type QwenContent struct {
	Image string `json:"image,omitempty"`
	Text  string `json:"text,omitempty"`
}

type QwenParameters struct {
	ResultFormat string `json:"result_format"`
}

// QwenResponse qwen API 响应结构
type QwenResponse struct {
	Output QwenOutput `json:"output"`
	Usage  QwenUsage  `json:"usage"`
}

type QwenOutput struct {
	Choices []QwenChoice `json:"choices"`
}

type QwenChoice struct {
	Message QwenResponseMessage `json:"message"`
}

type QwenResponseMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

type QwenUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// QwenAnalysisResult qwen 分析结果
type QwenAnalysisResult struct {
	RawContent string    `json:"raw_content"`
	Usage      QwenUsage `json:"usage"`
}

// DeepseekRequest deepseek API 请求结构
type DeepseekRequest struct {
	Model    string             `json:"model"`
	Messages []DeepseekMessage  `json:"messages"`
}

type DeepseekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// DeepseekResponse deepseek API 响应结构
type DeepseekResponse struct {
	Choices []DeepseekChoice `json:"choices"`
	Usage   DeepseekUsage    `json:"usage"`
}

type DeepseekChoice struct {
	Message DeepseekResponseMessage `json:"message"`
}

type DeepseekResponseMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepseekUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// DeepseekAnswerResult deepseek 答案结果
type DeepseekAnswerResult struct {
	Answer      string        `json:"answer"`
	Explanation string        `json:"explanation"`
	Usage       DeepseekUsage `json:"usage"`
}

// ProcessingLog 处理日志
type ProcessingLog struct {
	QwenLog     *APICallLog `json:"qwen_log"`
	DeepseekLog *APICallLog `json:"deepseek_log"`
}

// APICallLog API 调用日志
type APICallLog struct {
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	Duration     string    `json:"duration"`
	TokenUsage   interface{} `json:"token_usage"`
	Success      bool      `json:"success"`
	ErrorMessage string    `json:"error_message,omitempty"`
}
