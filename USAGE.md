# 使用指南

## 快速开始

### 1. 设置环境变量

在运行服务之前，需要设置 API Keys：

```bash
# 设置 Qwen API Key
export QWEN_API_KEY="your_qwen_api_key_here"

# 设置 Deepseek API Key  
export DEEPSEEK_API_KEY="your_deepseek_api_key_here"

# 可选：设置服务端口（默认 8080）
export SERVER_PORT="8080"
```

### 2. 启动服务

```bash
# 方式1：直接运行
go run main.go

# 方式2：编译后运行
go build -o go_solve_api .
./go_solve_api
```

服务启动后会显示：
```
Server starting on port 8080
Endpoints:
  POST /api/v1/exam/process - Process exam questions
  GET  /api/v1/health - Health check
  GET  / - Service information
```

### 3. 测试 API

#### 健康检查
```bash
curl -X GET http://localhost:8080/api/v1/health
```

#### 处理考试题目
```bash
curl -X POST http://localhost:8080/api/v1/exam/process \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/exam-image.jpg"
  }'
```

## API 响应示例

### 成功响应
```json
{
  "success": true,
  "message": "Exam processed successfully",
  "qwen_result": {
    "raw_content": "1. 题目类型：单选题\n2. 题目内容：驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？\n3. 选项内容：\nA、减速鸣喇叭示意\nB、迅速行驶到坡顶以改善视距\nC、长时间开启远光灯提醒对向来车\nD、不间断鸣喇叭并加速冲坡",
    "usage": {
      "input_tokens": 150,
      "output_tokens": 80,
      "total_tokens": 230
    }
  },
  "deepseek_result": {
    "answer": "A",
    "explanation": "在驾驶机动车驶近坡道顶端、视距不良的情况下，应该减速鸣喇叭示意。这是因为：1. 减速可以确保有足够的反应时间；2. 鸣喇叭可以提醒对向来车注意；3. 其他选项都存在安全隐患。",
    "usage": {
      "prompt_tokens": 200,
      "completion_tokens": 120,
      "total_tokens": 320
    }
  },
  "processing_log": {
    "qwen_log": {
      "start_time": "2024-01-01T10:00:00Z",
      "end_time": "2024-01-01T10:00:02Z",
      "duration": "2.1s",
      "token_usage": {...},
      "success": true
    },
    "deepseek_log": {
      "start_time": "2024-01-01T10:00:02Z",
      "end_time": "2024-01-01T10:00:04Z",
      "duration": "1.8s",
      "token_usage": {...},
      "success": true
    }
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "Failed to analyze image with Qwen: API returned status 401: Unauthorized"
}
```

## 支持的题型

### 单选题示例
```
驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？
A、减速鸣喇叭示意
B、迅速行驶到坡顶以改善视距
C、长时间开启远光灯提醒对向来车
D、不间断鸣喇叭并加速冲坡
```

### 多选题示例
```
当你驾车准备进入拥堵的环形路口时，以下哪些做法是不安全的?
A、继续驶入拥堵路口
B、让路口内的车先行
C、快速驶入路口
D、鸣喇叭催促路口内车辆
```

### 判断题示例
```
驾驶人在道路上行驶，要时刻留意人行横道标志，遇有行人通过人行横道时，应停车让行。
Y：正确
N：错误
```

## 注意事项

1. **API Keys 必须有效**：确保 Qwen 和 Deepseek 的 API Keys 都是有效的
2. **图片 URL 可访问**：提供的图片 URL 必须是公开可访问的
3. **网络连接**：确保服务器能够访问外部 API
4. **图片质量**：图片应该清晰，文字可读
5. **日志监控**：服务会输出详细的 JSON 格式日志，便于监控和调试

## 故障排除

### 常见错误

1. **API Key 错误**
   ```
   Failed to analyze image with Qwen: API returned status 401: Unauthorized
   ```
   解决：检查 QWEN_API_KEY 环境变量是否正确设置

2. **图片 URL 无法访问**
   ```
   Failed to analyze image with Qwen: API returned status 400: Invalid image URL
   ```
   解决：确保图片 URL 是公开可访问的

3. **网络超时**
   ```
   HTTP request failed: context deadline exceeded
   ```
   解决：检查网络连接，API 服务是否正常

### 调试技巧

1. 查看详细日志：服务会输出 JSON 格式的详细日志
2. 使用健康检查接口确认服务状态
3. 先测试简单的图片 URL，确保基本功能正常
