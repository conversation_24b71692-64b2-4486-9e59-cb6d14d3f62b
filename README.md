# Go Solve API

一个使用 Go 语言开发的 Web API 服务，用于处理考试题目图片识别和答案解析。

## 功能特性

- 接收图片 URL，调用 Qwen-VL-Plus 识别考试题目内容
- 将识别结果发送给 Deepseek-Chat 获取答案和解析
- 详细的日志记录，包括响应时间和 token 消耗统计
- 专门的 Qwen 文本日志输出（时间 + 内容格式）
- 支持单选题、多选题、判断题三种题型
- RESTful API 设计

## 业务流程

1. 客户端提交图片 URL
2. 调用 Qwen-VL-Plus 识别图片中的考试题目
3. 将 Qwen 的识别结果发送给 Deepseek-Chat
4. Deepseek 返回正确答案和详细解析
5. 记录完整的处理日志和性能指标

## 安装和运行

### 环境要求

- Go 1.24.0 或更高版本
- Qwen API Key
- Deepseek API Key

### 配置环境变量

复制 `.env.example` 文件并重命名为 `.env`，然后填入你的 API Keys：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
QWEN_API_KEY=your_qwen_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
SERVER_PORT=8080
```

### 运行服务

```bash
# 安装依赖
go mod tidy

# 运行服务
go run main.go
```

服务将在 `http://localhost:8080` 启动。

## API 接口

### 1. 处理考试题目

**POST** `/api/v1/exam/process`

**请求体：**
```json
{
    "image_url": "https://example.com/exam-image.jpg"
}
```

**响应：**
```json
{
    "success": true,
    "message": "Exam processed successfully",
    "qwen_result": {
        "raw_content": "识别的题目内容...",
        "usage": {
            "input_tokens": 100,
            "output_tokens": 200,
            "total_tokens": 300
        }
    },
    "deepseek_result": {
        "answer": "A",
        "explanation": "详细的答案解析...",
        "usage": {
            "prompt_tokens": 150,
            "completion_tokens": 250,
            "total_tokens": 400
        }
    },
    "processing_log": {
        "qwen_log": {
            "start_time": "2024-01-01T10:00:00Z",
            "end_time": "2024-01-01T10:00:02Z",
            "duration": "2s",
            "token_usage": {...},
            "success": true
        },
        "deepseek_log": {
            "start_time": "2024-01-01T10:00:02Z",
            "end_time": "2024-01-01T10:00:04Z",
            "duration": "2s",
            "token_usage": {...},
            "success": true
        }
    }
}
```

### 2. 健康检查

**GET** `/api/v1/health`

**响应：**
```json
{
    "status": "healthy",
    "service": "go_solve_api",
    "version": "1.0.0"
}
```

### 3. 服务信息

**GET** `/`

**响应：**
```json
{
    "service": "go_solve_api",
    "version": "1.0.0",
    "endpoints": {
        "process_exam": "/api/v1/exam/process",
        "health_check": "/api/v1/health"
    },
    "description": "API service for processing exam questions using Qwen and Deepseek"
}
```

## 测试

使用 cURL 测试 API：

```bash
# 健康检查
curl -X GET http://localhost:8080/api/v1/health

# 处理考试题目
curl -X POST http://localhost:8080/api/v1/exam/process \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/your-exam-image.jpg"
  }'
```

## 项目结构

```
go_solve_api/
├── main.go                 # 主程序入口
├── config/
│   └── config.go          # 配置管理
├── models/
│   └── types.go           # 数据结构定义
├── services/
│   ├── qwen_service.go    # Qwen API 服务
│   └── deepseek_service.go # Deepseek API 服务
├── handlers/
│   └── exam_handler.go    # HTTP 处理器
├── utils/
│   └── logger.go          # 日志工具
├── .env.example           # 环境变量示例
└── README.md              # 项目文档
```

## 注意事项

1. 确保提供有效的 Qwen 和 Deepseek API Keys
2. 图片 URL 必须是公开可访问的
3. 服务会记录详细的处理日志，包括 token 消耗和响应时间
4. Qwen 的请求格式严格按照官方规范，确保最佳识别效果
