package config

import (
	"os"
)

type Config struct {
	QwenAPIKey      string
	QwenAPIURL      string
	QwenModel       string
	Deepseek<PERSON><PERSON>Key  string
	DeepseekAPIURL  string
	DeepseekModel   string
	ServerPort      string
}

func LoadConfig() *Config {
	return &Config{
		QwenAPIKey:      getEnvOrDefault("QWEN_API_KEY", ""),
		QwenAPIURL:      getEnvOrDefault("QWEN_API_URL", "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"),
		QwenModel:       getEnvOrDefault("QWEN_MODEL", "qwen-vl-plus"),
		DeepseekAPIKey:  getEnvOrDefault("DEEPSEEK_API_KEY", ""),
		DeepseekAPIURL:  getEnvOrDefault("DEEPSEEK_API_URL", "https://api.deepseek.com/chat/completions"),
		DeepseekModel:   getEnvOrDefault("DEEPSEEK_MODEL", "deepseek-chat"),
		ServerPort:      getEnvOrDefault("SERVER_PORT", "8080"),
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
