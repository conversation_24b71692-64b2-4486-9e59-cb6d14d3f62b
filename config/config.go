package config

import (
	"bufio"
	"os"
	"strings"
)

type Config struct {
	QwenAPIKey      string
	QwenAPIURL      string
	QwenModel       string
	DeepseekAPIKey  string
	DeepseekAPIURL  string
	DeepseekModel   string
	ServerPort      string
}

func LoadConfig() *Config {
	// 尝试加载 .env 文件
	loadEnvFile()

	return &Config{
		QwenAPIKey:      getEnvOrDefault("QWEN_API_KEY", ""),
		QwenAPIURL:      getEnvOrDefault("QWEN_API_URL", "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"),
		QwenModel:       getEnvOrDefault("QWEN_MODEL", "qwen-vl-plus"),
		DeepseekAPIKey:  getEnvOrDefault("DEEPSEEK_API_KEY", ""),
		DeepseekAPIURL:  getEnvOrDefault("DEEPSEEK_API_URL", "https://api.deepseek.com/chat/completions"),
		DeepseekModel:   getEnvOrDefault("DEEPSEEK_MODEL", "deepseek-chat"),
		ServerPort:      getEnvOrDefault("SERVER_PORT", "8080"),
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// loadEnvFile 加载 .env 文件中的环境变量
func loadEnvFile() {
	file, err := os.Open(".env")
	if err != nil {
		// .env 文件不存在或无法打开，忽略错误
		return
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析 KEY=VALUE 格式
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			// 只有当环境变量未设置时才设置
			if os.Getenv(key) == "" {
				os.Setenv(key, value)
			}
		}
	}
}
