帮我创建一个python的虚拟环境。并配置好相关依赖。你需要先分析下述需求，然后在开始处理。


直接 HTTP REST API 调用qwen跟deepseek。不要集成sdk

业务流程是

1. 将图片的url提交给qwen-vl-plus大模型。
2. 将qwen返回的原始数据发送给deepseek-chat大模型。
3. 得到deepseek-chat返回的最终结果。
4. 需要输出日志
    4.1. 需要记录qwen-vl-plus的返回结果,需要记录qwen-vl-plus的响应时间以及token的消耗情况
    4.2. 需要记录deepseek-chat的返回结果，需要记录qdeepseek的响应时间以及token的消耗情况

图片的解释；

所有的图片都是一个考试系统的截图或者照片，图片内容有考试题。需要qwen精准的识别图中的考题与题干图片，

问题示例样本，共有三种问题。三种题型都有可能存在图片，题干图片位置在问题选项的下方。

(单选题)
驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？
A、减速鸣喇叭示意
B、迅速行驶到坡顶以改善视距
C、长时间开启远光灯提醒对向来车
D、不间断鸣喇叭并加速冲坡


(多选题)
当你驾车准备进入拥堵的环形路口时，以下哪些做法是不安全的?
A、继续驶入拥堵路口
B、让路口内的车先行
C、快速驶入路口
D、鸣喇叭催促路口内车辆


(判断题)
驾驶人在道路上行驶，要时刻留意人行横道标志，遇有行人通过人行横道时，应停车让行。
Y：正确
N：错误


对于qwen-vl-plus的返回结果，我期望的是完整且精准的识别出考试问题与选项的问题。不需要qwen给我解答问题本身。


对于deepseek-chat的返回结果，我期望的是能给我回复考试问题的准确答案与解析。
我需要deepseek给我回复两个信息，【正确答案】【答案解析】


完成后给我一个可以用于测试的cURL。