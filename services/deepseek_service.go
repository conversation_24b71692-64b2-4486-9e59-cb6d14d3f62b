package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"go_solve_api/config"
	"go_solve_api/models"
	"go_solve_api/utils"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"
)

type DeepseekService struct {
	config *config.Config
	logger *utils.Logger
}

func NewDeepseekService(cfg *config.Config, logger *utils.Logger) *DeepseekService {
	return &DeepseekService{
		config: cfg,
		logger: logger,
	}
}

func (s *DeepseekService) AnswerQuestion(qwenContent string) (*models.DeepseekAnswerResult, *models.APICallLog, error) {
	startTime := time.Now()

	prompt := fmt.Sprintf(`这是一道驾照考试的题目。如果题目内容存在重大问题、缺少关键信息或无法准确判断答案，请回复"题意出错"。

题目内容：
%s

请基于您的驾驶法规知识库，需要保证答案的置信度为100%%。
需要严格按照以下格式返回内容：

题目类型：[单选题/多选题/判断题]
题目内容：[问题的完整内容，不包含序号]
选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]
正确答案：[A,B,C,D]
答案解析：[详细的法规依据和解题思路]

重要约束：
1. 判断题时需要把Y与N映射为A与B
2. 所有的标点符号都需要用英文符号
3. 不可以出现多余的空格
4. 答案解析必须包含具体的法规条文或安全原理
5. 严格按照上述格式输出，不要添加任何其他内容`, qwenContent)

	payload := models.DeepseekRequest{
		Model: s.config.DeepseekModel,
		Messages: []models.DeepseekMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to marshal request: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	req, err := http.NewRequest("POST", s.config.DeepseekAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create request: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.DeepseekAPIKey)

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("HTTP request failed: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}
	defer resp.Body.Close()

	endTime := time.Now()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to read response: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	if resp.StatusCode != http.StatusOK {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("API returned status %d: %s", resp.StatusCode, string(body)),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var deepseekResp models.DeepseekResponse
	if err := json.Unmarshal(body, &deepseekResp); err != nil {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to unmarshal response: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	if len(deepseekResp.Choices) == 0 {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: "No choices in response",
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, deepseekResp.Usage, false, apiLog.ErrorMessage)
		return nil, apiLog, fmt.Errorf("no choices in response")
	}

	content := deepseekResp.Choices[0].Message.Content
	parsedResult := s.parseStructuredResponse(content)

	result := &models.DeepseekAnswerResult{
		QuestionType:    parsedResult.QuestionType,
		QuestionContent: parsedResult.QuestionContent,
		Options:         parsedResult.Options,
		Answer:          parsedResult.Answer,
		Explanation:     parsedResult.Explanation,
		RawResponse:     content,
		Usage:           deepseekResp.Usage,
	}

	apiLog := &models.APICallLog{
		StartTime:  startTime,
		EndTime:    endTime,
		Duration:   endTime.Sub(startTime).String(),
		TokenUsage: deepseekResp.Usage,
		Success:    true,
	}

	s.logger.APICall("deepseek-chat", startTime, endTime, deepseekResp.Usage, true, "")
	s.logger.Info("Deepseek analysis completed", map[string]interface{}{
		"answer":      result.Answer,
		"token_usage": result.Usage,
	})

	return result, apiLog, nil
}

// ParsedResponse 解析后的响应结构
type ParsedResponse struct {
	QuestionType    string
	QuestionContent string
	Options         string
	Answer          string
	Explanation     string
}

func (s *DeepseekService) parseStructuredResponse(content string) *ParsedResponse {
	result := &ParsedResponse{}

	// 检查是否回复了"题意出错"
	if strings.Contains(content, "题意出错") {
		result.Answer = "题意出错"
		result.Explanation = "题目内容存在重大问题，无法分析正确答案"
		return result
	}

	// 解析各个字段
	result.QuestionType = s.extractField(content, "题目类型")
	result.QuestionContent = s.extractField(content, "题目内容")
	result.Options = s.extractField(content, "选项内容")
	result.Answer = s.extractField(content, "正确答案")
	result.Explanation = s.extractField(content, "答案解析")

	return result
}

func (s *DeepseekService) extractField(content, fieldName string) string {
	// 使用正则表达式提取方括号内的内容
	pattern := fmt.Sprintf(`%s[：:]\s*\[([^\]]+)\]`, regexp.QuoteMeta(fieldName))
	regex := regexp.MustCompile(pattern)

	if matches := regex.FindStringSubmatch(content); len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	// 如果正则匹配失败，尝试简单的字符串分割
	lines := strings.Split(content, "\n")
	for _, line := range lines {
		if strings.Contains(line, fieldName) {
			// 提取方括号内的内容
			if start := strings.Index(line, "["); start != -1 {
				if end := strings.Index(line[start:], "]"); end != -1 {
					return strings.TrimSpace(line[start+1 : start+end])
				}
			}
		}
	}

	return ""
}

func (s *DeepseekService) parseAnswerAndExplanation(content string) (string, string) {
	// 检查是否回复了"题意出错"
	if strings.Contains(content, "题意出错") {
		return "题意出错", "题目内容存在重大问题，无法分析正确答案"
	}

	// 使用正则表达式提取答案和解析（新格式）
	answerRegex := regexp.MustCompile(`正确答案[：:]\s*\[([^\]]+)\]`)
	explanationRegex := regexp.MustCompile(`答案解析[：:]\s*\[([^\]]+)\]`)

	var answer, explanation string

	if matches := answerRegex.FindStringSubmatch(content); len(matches) > 1 {
		answer = strings.TrimSpace(matches[1])
	}

	if matches := explanationRegex.FindStringSubmatch(content); len(matches) > 1 {
		explanation = strings.TrimSpace(matches[1])
	}

	// 如果正则匹配失败，尝试简单的字符串分割
	if answer == "" || explanation == "" {
		lines := strings.Split(content, "\n")
		for i, line := range lines {
			if strings.Contains(line, "正确答案") && answer == "" {
				// 提取方括号内的内容
				if start := strings.Index(line, "["); start != -1 {
					if end := strings.Index(line[start:], "]"); end != -1 {
						answer = strings.TrimSpace(line[start+1 : start+end])
					}
				}
			}
			if strings.Contains(line, "答案解析") && explanation == "" {
				// 提取方括号内的内容
				if start := strings.Index(line, "["); start != -1 {
					if end := strings.Index(line[start:], "]"); end != -1 {
						explanation = strings.TrimSpace(line[start+1 : start+end])
					}
				}
				// 如果解析内容在下一行，尝试获取
				if explanation == "" && i+1 < len(lines) {
					nextLine := strings.TrimSpace(lines[i+1])
					if start := strings.Index(nextLine, "["); start != -1 {
						if end := strings.Index(nextLine[start:], "]"); end != -1 {
							explanation = strings.TrimSpace(nextLine[start+1 : start+end])
						}
					}
				}
			}
		}
	}

	// 如果仍然为空，返回原始内容
	if answer == "" && explanation == "" {
		return content, content
	}

	return answer, explanation
}
