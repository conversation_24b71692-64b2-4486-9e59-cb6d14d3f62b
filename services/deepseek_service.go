package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"go_solve_api/config"
	"go_solve_api/models"
	"go_solve_api/utils"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"
)

type DeepseekService struct {
	config *config.Config
	logger *utils.Logger
}

func NewDeepseekService(cfg *config.Config, logger *utils.Logger) *DeepseekService {
	return &DeepseekService{
		config: cfg,
		logger: logger,
	}
}

func (s *DeepseekService) AnswerQuestion(qwenContent string) (*models.DeepseekAnswerResult, *models.APICallLog, error) {
	startTime := time.Now()

	prompt := fmt.Sprintf(`请根据以下考试题目内容，提供准确的答案和详细解析。

题目内容：
%s

请按照以下格式回复：
【正确答案】：（请提供准确的答案，如：A、B、C、D 或 Y、N 或 ACD 等）
【答案解析】：（请提供详细的解析说明，包括解题思路和知识点）

要求：
1. 答案必须准确
2. 解析要详细清晰
3. 严格按照上述格式输出`, qwenContent)

	payload := models.DeepseekRequest{
		Model: s.config.DeepseekModel,
		Messages: []models.DeepseekMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to marshal request: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	req, err := http.NewRequest("POST", s.config.DeepseekAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create request: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.DeepseekAPIKey)

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("HTTP request failed: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}
	defer resp.Body.Close()

	endTime := time.Now()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to read response: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	if resp.StatusCode != http.StatusOK {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("API returned status %d: %s", resp.StatusCode, string(body)),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var deepseekResp models.DeepseekResponse
	if err := json.Unmarshal(body, &deepseekResp); err != nil {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to unmarshal response: %v", err),
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	if len(deepseekResp.Choices) == 0 {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: "No choices in response",
		}
		s.logger.APICall("deepseek-chat", startTime, endTime, deepseekResp.Usage, false, apiLog.ErrorMessage)
		return nil, apiLog, fmt.Errorf("no choices in response")
	}

	content := deepseekResp.Choices[0].Message.Content
	answer, explanation := s.parseAnswerAndExplanation(content)

	result := &models.DeepseekAnswerResult{
		Answer:      answer,
		Explanation: explanation,
		Usage:       deepseekResp.Usage,
	}

	apiLog := &models.APICallLog{
		StartTime:  startTime,
		EndTime:    endTime,
		Duration:   endTime.Sub(startTime).String(),
		TokenUsage: deepseekResp.Usage,
		Success:    true,
	}

	s.logger.APICall("deepseek-chat", startTime, endTime, deepseekResp.Usage, true, "")
	s.logger.Info("Deepseek analysis completed", map[string]interface{}{
		"answer":      result.Answer,
		"token_usage": result.Usage,
	})

	return result, apiLog, nil
}

func (s *DeepseekService) parseAnswerAndExplanation(content string) (string, string) {
	// 使用正则表达式提取答案和解析
	answerRegex := regexp.MustCompile(`【正确答案】[：:]\s*(.+?)(?:\n|【|$)`)
	explanationRegex := regexp.MustCompile(`【答案解析】[：:]\s*(.+?)(?:$|\n\n)`)

	var answer, explanation string

	if matches := answerRegex.FindStringSubmatch(content); len(matches) > 1 {
		answer = strings.TrimSpace(matches[1])
	}

	if matches := explanationRegex.FindStringSubmatch(content); len(matches) > 1 {
		explanation = strings.TrimSpace(matches[1])
	}

	// 如果正则匹配失败，尝试简单的字符串分割
	if answer == "" || explanation == "" {
		lines := strings.Split(content, "\n")
		for i, line := range lines {
			if strings.Contains(line, "正确答案") && answer == "" {
				parts := strings.Split(line, "：")
				if len(parts) < 2 {
					parts = strings.Split(line, ":")
				}
				if len(parts) >= 2 {
					answer = strings.TrimSpace(parts[1])
				}
			}
			if strings.Contains(line, "答案解析") && explanation == "" {
				parts := strings.Split(line, "：")
				if len(parts) < 2 {
					parts = strings.Split(line, ":")
				}
				if len(parts) >= 2 {
					explanation = strings.TrimSpace(parts[1])
					// 如果解析内容在下一行，尝试获取
					if explanation == "" && i+1 < len(lines) {
						explanation = strings.TrimSpace(lines[i+1])
					}
				}
			}
		}
	}

	// 如果仍然为空，返回原始内容
	if answer == "" && explanation == "" {
		return content, content
	}

	return answer, explanation
}
