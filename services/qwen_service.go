package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"go_solve_api/config"
	"go_solve_api/models"
	"go_solve_api/utils"
	"io"
	"net/http"
	"time"
)

type QwenService struct {
	config *config.Config
	logger *utils.Logger
}

func NewQwenService(cfg *config.Config, logger *utils.Logger) *QwenService {
	return &QwenService{
		config: cfg,
		logger: logger,
	}
}

func (s *QwenService) AnalyzeImage(imageURL string) (*models.QwenAnalysisResult, *models.APICallLog, error) {
	startTime := time.Now()
	
	// 严格按照提供的 Python 方案构建请求，只做语法调整
	payload := models.QwenRequest{
		Model: s.config.QwenModel,
		Input: models.QwenInput{
			Messages: []models.QwenMessage{
				{
					Role: "user",
					Content: []models.QwenContent{
						{
							Image: imageURL,
						},
						{
							Text: `请精准识别图片中的考试题目内容。图片是考试系统的截图，包含考试题目和选项。

请按照以下格式输出：
1. 题目类型：（单选题/多选题/判断题）
2. 题目内容：（完整的题干）
3. 选项内容：（所有选项的完整内容）
4. 题干图片：（如果题目下方有图片，请描述图片内容）

要求：
- 完整准确地识别所有文字内容
- 不要解答题目，只需要识别题目内容
- 保持原有的格式和选项标识（A、B、C、D或Y、N）`,
						},
					},
				},
			},
		},
		Parameters: models.QwenParameters{
			ResultFormat: "message",
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to marshal request: %v", err),
		}
		s.logger.APICall("qwen-vl-plus", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	req, err := http.NewRequest("POST", s.config.QwenAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to create request: %v", err),
		}
		s.logger.APICall("qwen-vl-plus", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.config.QwenAPIKey)

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		endTime := time.Now()
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("HTTP request failed: %v", err),
		}
		s.logger.APICall("qwen-vl-plus", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}
	defer resp.Body.Close()

	endTime := time.Now()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to read response: %v", err),
		}
		s.logger.APICall("qwen-vl-plus", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	if resp.StatusCode != http.StatusOK {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("API returned status %d: %s", resp.StatusCode, string(body)),
		}
		s.logger.APICall("qwen-vl-plus", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var qwenResp models.QwenResponse
	if err := json.Unmarshal(body, &qwenResp); err != nil {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: fmt.Sprintf("Failed to unmarshal response: %v", err),
		}
		s.logger.APICall("qwen-vl-plus", startTime, endTime, nil, false, apiLog.ErrorMessage)
		return nil, apiLog, err
	}

	if len(qwenResp.Output.Choices) == 0 {
		apiLog := &models.APICallLog{
			StartTime:    startTime,
			EndTime:      endTime,
			Duration:     endTime.Sub(startTime).String(),
			Success:      false,
			ErrorMessage: "No choices in response",
		}
		s.logger.APICall("qwen-vl-plus", startTime, endTime, qwenResp.Usage, false, apiLog.ErrorMessage)
		return nil, apiLog, fmt.Errorf("no choices in response")
	}

	result := &models.QwenAnalysisResult{
		RawContent: qwenResp.Output.Choices[0].Message.Content,
		Usage:      qwenResp.Usage,
	}

	apiLog := &models.APICallLog{
		StartTime:  startTime,
		EndTime:    endTime,
		Duration:   endTime.Sub(startTime).String(),
		TokenUsage: qwenResp.Usage,
		Success:    true,
	}

	s.logger.APICall("qwen-vl-plus", startTime, endTime, qwenResp.Usage, true, "")
	s.logger.Info("Qwen analysis completed", map[string]interface{}{
		"content_length": len(result.RawContent),
		"token_usage":    result.Usage,
	})

	return result, apiLog, nil
}
